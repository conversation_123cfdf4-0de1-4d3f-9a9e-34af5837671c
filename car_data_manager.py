"""
Car Data Manager
Loads car data from JSON files in the JSON folder
"""

import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CarDataManager:
    """Manages car data loaded from Python files"""
    
    def __init__(self):
        self.arena_cars = {}
        self.nexa_cars = {}
        self.last_loaded = None
        self.load_all_car_data()
    
    def load_all_car_data(self):
        """Load all car data from cars folder Python files"""
        try:
            # Load Arena car data from Python file
            try:
                from cars.arena_data import cars_data as arena_cars_data
                self.arena_cars = arena_cars_data
            except ImportError as e:
                logger.warning(f"⚠️ Could not import arena_data: {e}")
                self.arena_cars = {}

            # Load Nexa car data from Python file
            try:
                from cars.nexa_data import cars_data as nexa_cars_data
                self.nexa_cars = nexa_cars_data
            except ImportError as e:
                logger.warning(f"⚠️ Could not import nexa_data: {e}")
                self.nexa_cars = {}

            self.last_loaded = datetime.now()

            return True
        except Exception as e:
            logger.error(f"❌ Error loading car data: {e}")
            return False
    
    def get_car_data(self, car_name: str, dealership_type: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """
        Get car data by name and optionally dealership type
        
        Args:
            car_name: Name of the car (case-insensitive)
            dealership_type: Optional dealership type ('arena' or 'nexa')
            
        Returns:
            Car data dictionary or None if not found
        """
        car_name_lower = car_name.lower().replace(' ', '_').replace('-', '_')

        if dealership_type:
            if dealership_type.lower() == 'arena':
                return self._find_car_in_data(car_name_lower, self.arena_cars)
            elif dealership_type.lower() == 'nexa':
                return self._find_car_in_data(car_name_lower, self.nexa_cars)
            return None

        car_data = self._find_car_in_data(car_name_lower, self.arena_cars)
        if car_data:
            return car_data

        return self._find_car_in_data(car_name_lower, self.nexa_cars)
    
    def _find_car_in_data(self, car_key: str, car_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Find car in data by key or by name (case-insensitive)"""
        if car_key in car_data:
            return car_data[car_key]

        for key, data in car_data.items():
            if data.get('name', '').lower() == car_key.replace('_', ' '):
                return data

        return None
    
    def get_all_arena_cars(self) -> Dict[str, Any]:
        """Get all Arena cars"""
        return self.arena_cars
    
    def get_all_nexa_cars(self) -> Dict[str, Any]:
        """Get all Nexa cars"""
        return self.nexa_cars
    
    def get_all_cars(self) -> Dict[str, Dict[str, Any]]:
        """Get all cars from both dealerships"""
        return {
            'arena': self.arena_cars,
            'nexa': self.nexa_cars
        }
    
    def search_cars(self, query: str) -> List[Dict[str, Any]]:
        """
        Search for cars by name, category, or fuel type
        
        Args:
            query: Search query string
            
        Returns:
            List of matching car data dictionaries
        """
        query_lower = query.lower()
        results = []

        for car_key, car_data in self.arena_cars.items():
            if self._car_matches_query(car_data, query_lower):
                car_copy = car_data.copy()
                car_copy['dealership_type'] = 'Arena'
                results.append(car_copy)

        for car_key, car_data in self.nexa_cars.items():
            if self._car_matches_query(car_data, query_lower):
                car_copy = car_data.copy()
                car_copy['dealership_type'] = 'Nexa'
                results.append(car_copy)

        return results
    
    def _car_matches_query(self, car_data: Dict[str, Any], query: str) -> bool:
        """Check if car data matches the search query"""
        if query in car_data.get('name', '').lower():
            return True

        if query in car_data.get('category', '').lower():
            return True

        for fuel_type in car_data.get('fuel_types', []):
            if query in fuel_type.lower():
                return True

        return False
    
    def get_stats(self) -> Dict[str, Any]:
        """Get statistics about loaded car data"""
        return {
            'total_cars': len(self.arena_cars) + len(self.nexa_cars),
            'arena_cars': len(self.arena_cars),
            'nexa_cars': len(self.nexa_cars),
            'last_loaded': self.last_loaded.isoformat() if self.last_loaded else None
        }

car_data_manager = CarDataManager()
def get_car_data(car_name: str, dealership_type: Optional[str] = None) -> Optional[Dict[str, Any]]:
    """Get car data by name and optionally dealership type"""
    return car_data_manager.get_car_data(car_name, dealership_type)

def get_all_cars() -> Dict[str, Dict[str, Any]]:
    """Get all cars from both dealerships"""
    return car_data_manager.get_all_cars()

def search_cars(query: str) -> List[Dict[str, Any]]:
    """Search for cars by name, category, or fuel type"""
    return car_data_manager.search_cars(query)
