"""
This module contains all the mapping configurations for the Bhandari Auto API.
It serves as a single source of truth for all data mappings used in the application.
"""

# Mapping of car models to their Redis keys
CAR_MODEL_TO_REDIS_KEY = {
    # Arena Cars
    'Alto K10': 'alto_k10',
    'Brezza': 'brezza',
    'Celerio': 'celerio',
    'Dzire': 'dzire',
    'Eeco': 'eeco',
    'Ertiga': 'ertiga',
    'S-Presso': 's_presso',
    'S-presso': 's_presso',  # Handling different capitalizations
    'Swift': 'swift',
    'Wagon-R': 'wagon_r',

    # NEXA Cars
    'Baleno': 'baleno',
    'Ciaz': 'ciaz',
    'Fronx': 'fronx',
    'Grand Vitara': 'grand_vitara',
    'Ignis': 'ignis',
    'Jimny': 'jimny',
    'XL6': 'xl6',

    # Tata Commercial Vehicles
    'Ace-Pro EV': 'ace_pro_ev',
    'Ace-Pro Petrol': 'ace_pro_petrol',
    'Ace Pro Petrol': 'ace_pro_petrol',  # Alternative naming
    'Ace CNG 2.0 Bi-Fuel': 'ace_cng_2.0_bi_fuel',
    'Ace Gold Petrol': 'tata_ace_gold_petrol',
    'Ace Gold CNG Plus': 'ace_gold_cng_plus',
    'Ace Diesel': 'tata_ace_diesel',
    'Ace EV': 'tata_ace_ev',
    'Ace EV 1000': 'ace_ev_1000',
    'Ace Pro': 'tata_ace_pro',
    'Ace Pro Bi-Fuel': 'ace_pro_bi-fuel',
    'Ace Flex Fuel': 'tata_ace_flex_fuel',
    'Ace HT+': 'tata_ace_ht+',
    'Ace Zip': 'tata_ace_zip',
    'Ace Gold Diesel': 'tata_ace_gold_diesel',
    
    'Intra V10': 'tata_intra_v10',
    'Intra V20 Gold': 'tata_intra_v20_gold',
    'Intra V20 Winger': 'intra_v20_winger',
    'Intra V30 Gold': 'tata_intra_v30_gold',
    'Intra V50 Gold': 'tata_intra_v50_gold',
    'Intra V70 Gold': 'tata_intra_v70_gold',
    'Intra EV': 'tata_intra_ev',
    
    'Yodha-CNG': 'yodha_cng',
    'Yodha CNG': 'yodha_cng',  # Alternative name
    'Yodha Crew Cab 4x4': 'yodha_crew_cab_4x4',
    'Yodha Crew Cab 4x2': 'yodha_crew_cab_4x2',
    'Yodha Crew Cab': 'tata_yodha_crew_cab',
    'Yodha EX': 'tata_yodha_ex',
    'Yodha EX Crew Cab': 'yodha_ex_crew_cab',
    'Yodha EX Single Cab': 'yodha_ex_single_cab',
    'Yodha 1700': 'tata_yodha_1700',
    'Yodha 1200': 'tata_yodha_1200',
    'Yodha 2.0': 'tata_yodha_2.0',
    
    # Special entries
    'Main Menu': 'main_menu',
}

# Reverse mapping for Redis key to display name
REDIS_KEY_TO_CAR_MODEL = {v: k for k, v in CAR_MODEL_TO_REDIS_KEY.items()}

def get_redis_key(car_model: str) -> str:
    """
    Get the Redis key for a given car model.
    
    Args:
        car_model: The display name of the car model
        
    Returns:
        str: The corresponding Redis key, or None if not found
    """
    return CAR_MODEL_TO_REDIS_KEY.get(car_model)

def get_car_display_name(redis_key: str) -> str:
    """
    Get the display name for a given Redis key.
    
    Args:
        redis_key: The Redis key to look up
        
    Returns:
        str: The corresponding display name, or the original key if not found
    """
    return REDIS_KEY_TO_CAR_MODEL.get(redis_key, redis_key)

def get_all_car_names() -> list:
    """
    Get a list of all available car display names.
    
    Returns:
        list: Sorted list of all car display names
    """
    return sorted(CAR_MODEL_TO_REDIS_KEY.keys())

def get_all_redis_keys() -> list:
    """
    Get a list of all available Redis keys.
    
    Returns:
        list: Sorted list of all Redis keys
    """
    return sorted(set(CAR_MODEL_TO_REDIS_KEY.values()))

# Alternative name mappings to handle different naming conventions
CAR_NAME_MAPPINGS = {
    # Arena Cars
    'alto k10': 'Alto K10',
    'alto': 'Alto K10',
    'k10': 'Alto K10',
    'brezza': 'Brezza',
    'vitara brezza': 'Brezza',
    'celerio': 'Celerio',
    'dzire': 'Dzire',
    'desire': 'Dzire',
    'eeco': 'Eeco',
    'ertiga': 'Ertiga',
    's-presso': 'S-Presso',
    's presso': 'S-Presso',
    'spresso': 'S-Presso',
    'swift': 'Swift',
    'wagon-r': 'Wagon-R',
    'wagon r': 'Wagon-R',
    'wagonr': 'Wagon-R',
    
    # NEXA Cars
    'baleno': 'Baleno',
    'ciaz': 'Ciaz',
    'fronx': 'Fronx',
    'grand vitara': 'Grand Vitara',
    'vitara': 'Grand Vitara',
    'ignis': 'Ignis',
    'jimny': 'Jimny',
    'xl6': 'XL6',
    'xl-6': 'XL6',
    
    # Special entries
    'main menu': 'Main Menu',
    'menu': 'Main Menu',
}

def get_canonical_name(name: str) -> str:
    """
    Get the canonical name for a car from various name variations.
    
    Args:
        name: The input name to look up
        
    Returns:
        str: The canonical name if found, otherwise the original name
    """
    if not name:
        return name
        
    # First check exact match
    if name in CAR_MODEL_TO_REDIS_KEY:
        return name
        
    # Check case-insensitive match
    lower_name = name.lower()
    for display_name in CAR_MODEL_TO_REDIS_KEY.keys():
        if display_name.lower() == lower_name:
            return display_name
    
    # Check alternative mappings
    if lower_name in CAR_NAME_MAPPINGS:
        return CAR_NAME_MAPPINGS[lower_name]
        
    # Check if it's a Redis key
    if name in REDIS_KEY_TO_CAR_MODEL:
        return REDIS_KEY_TO_CAR_MODEL[name]
        
    # Check if it's a Redis key case-insensitively
    lower_redis_keys = {k.lower(): k for k in REDIS_KEY_TO_CAR_MODEL}
    if lower_name in lower_redis_keys:
        return REDIS_KEY_TO_CAR_MODEL[lower_redis_keys[lower_name]]
        
    return name
