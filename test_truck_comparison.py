#!/usr/bin/env python3
"""
Test script for truck comparison functionality
"""

import sys
sys.path.append('.')

from bhandari_auto_api_v4 import (
    compare_trucks_with_data,
    generate_detailed_comparison_response,
    get_all_truck_names,
    is_comparison_query
)

def test_truck_comparison():
    """Test truck comparison functionality"""
    print("🚛 Testing Truck Comparison System")
    print("=" * 50)
    
    # Test 1: Get all truck names
    print("\n1. Testing get_all_truck_names():")
    truck_names = get_all_truck_names()
    print(f"   Total truck names loaded: {len(truck_names)}")
    print(f"   Sample names: {sorted(truck_names)[:10]}")
    
    # Test 2: Direct truck comparison
    print("\n2. Testing compare_trucks_with_data():")
    test_cases = [
        ("ace pro ev", "ultra t.6"),
        ("LPT 710", "LPT 712"),
        ("Ace Pro EV", "Tata ULTRA T.6"),
    ]
    
    for truck1, truck2 in test_cases:
        print(f"\n   Comparing: {truck1} vs {truck2}")
        result = compare_trucks_with_data(truck1, truck2)
        if result:
            print("   ✅ Comparison successful")
            print(f"   Preview: {result[:100]}...")
        else:
            print("   ❌ Comparison failed")
    
    # Test 3: Full API integration
    print("\n3. Testing generate_detailed_comparison_response():")
    test_prompts = [
        "Compare ace pro ev vs ultra t.6",
        "Compare LPT 710 vs LPT 712",
        "Which is better between Ace Pro EV and ULTRA T.6?",
    ]
    
    for prompt in test_prompts:
        print(f"\n   Prompt: {prompt}")
        result = generate_detailed_comparison_response(prompt)
        if result:
            print("   ✅ API comparison successful")
            print(f"   Preview: {result[:100]}...")
        else:
            print("   ❌ API comparison failed")
    
    # Test 4: Comparison query detection
    print("\n4. Testing is_comparison_query():")
    test_queries = [
        "Compare ace pro ev vs ultra t.6",
        "Tell me about ace pro ev",
        "Which is better LPT 710 or LPT 712?",
        "Show me truck specifications",
    ]
    
    for query in test_queries:
        is_comparison = is_comparison_query(query)
        print(f"   '{query}' -> {'✅ Comparison' if is_comparison else '❌ Not comparison'}")
    
    print("\n" + "=" * 50)
    print("🎉 Truck comparison testing completed!")

if __name__ == "__main__":
    test_truck_comparison()
