import os
from typing import Dict, Any
import json
from collections import defaultdict

# def get_car_action(car_name: str, action: str) -> Dict[str, Any]:
#     """
#     Get a specific action for a car using namespacing

#     Args:
#         car_name (str): Name of the car (e.g., "Swift", "Brezza")
#         action (str): Action name (e.g., "Ex-showroom Price", "LXI")

#     Returns:
#         dict: Configuration for the action, or None if not found
#     """
#     # Try namespaced key first
#     namespaced_key = f"{car_name}::{action}"
#     if namespaced_key in WHATSAPP_CONFIG:
#         return {
#             "status": "success",
#             "source": "namespaced",
#             "key": namespaced_key,
#             "config": WHATSAPP_CONFIG[namespaced_key]
#         }

#     # Try structured format
#     if car_name in NAMESPACED_CONFIG and action in NAMESPACED_CONFIG[car_name]:
#         return {
#             "status": "success",
#             "source": "structured",
#             "key": f"{car_name}.{action}",
#             "config": NAMESPACED_CONFIG[car_name][action]
#         }

#     # Fallback to legacy format
#     legacy_key = f"{car_name} {action}"
#     if legacy_key in WHATSAPP_CONFIG:
#         return {
#             "status": "success",
#             "source": "legacy",
#             "key": legacy_key,
#             "config": WHATSAPP_CONFIG[legacy_key]
#         }

#     return {
#         "status": "not_found",
#         "message": f"Action '{action}' not found for car '{car_name}'"
#     }

# def get_available_car_actions(car_name: str) -> list:
#     """
#     Get all available actions for a specific car

#     Args:
#         car_name (str): Name of the car

#     Returns:
#         list: List of available actions for the car
#     """
#     actions = []

#     # Get from structured format
#     if car_name in NAMESPACED_CONFIG:
#         actions.extend(NAMESPACED_CONFIG[car_name].keys())

#     # Get from namespaced keys
#     namespace_prefix = f"{car_name}::"
#     for key in WHATSAPP_CONFIG.keys():
#         if key.startswith(namespace_prefix):
#             action = key.replace(namespace_prefix, "")
#             if action not in actions:
#                 actions.append(action)

#     # Get from legacy format
#     for key in WHATSAPP_CONFIG.keys():
#         if key.startswith(car_name + " ") and key != car_name:
#             action = key.replace(car_name + " ", "")
#             if action not in actions:
#                 actions.append(action)

#     return sorted(list(set(actions)))

# def list_all_cars_with_actions() -> Dict[str, list]:
#     """
#     Get a dictionary of all cars and their available actions

#     Returns:
#         dict: {car_name: [list_of_actions]}
#     """
#     cars_actions = {}

#     # From structured format
#     for car_name in NAMESPACED_CONFIG.keys():
#         cars_actions[car_name] = get_available_car_actions(car_name)

#     # From namespaced keys
#     for key in WHATSAPP_CONFIG.keys():
#         if "::" in key:
#             car_name, action = key.split("::", 1)
#             if car_name not in cars_actions:
#                 cars_actions[car_name] = []
#             if action not in cars_actions[car_name]:
#                 cars_actions[car_name].append(action)

#     # Clean up and sort
#     for car_name in cars_actions:
#         cars_actions[car_name] = sorted(list(set(cars_actions[car_name])))

#     return cars_actions

def load_text_file(filename: str) -> str:
    """
    Loads text content from a specified file.

    Args:
        filename (str): The path to the text file.

    Returns:
        str: The content of the file, or an empty string if not found/error.
    """
    try:
        if os.path.exists(filename):
            with open(filename, 'r', encoding='utf-8') as file:
                return file.read().strip()
        else:
            print(f"Warning: File {filename} not found")
            return ""
    except Exception as e:
        print(f"Error loading file {filename}: {e}")
        return ""


def format_arena_cars_message(arena_cars: list, dealership: Dict[str, Any] = None) -> Dict[str, Any]:
    """Format Arena cars listing with WhatsApp-friendly format"""
    message = f"🏟️ *ARENA SHOWROOM*\n"
    message += f"━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n"
    message += f"🚗 *AVAILABLE MODELS* ({len(arena_cars)} cars)\n"
    message += f"─────────────────────────────\n"

    buttons = []
    for i, car in enumerate(arena_cars, 1):
        category = car.get('category', '').lower()
        if "suv" in category or "muv" in category:
            emoji = "🚙"
        elif "hatch" in category:
            emoji = "🚗"
        elif "sedan" in category:
            emoji = "🚘"
        else:
            emoji = "🚗"

        message += f"{i}. {emoji} *{car['name']}* - {car.get('category', 'N/A')}\n"

    message += f"\n👆 *Select a car to view details:*"

    return {
        "status": "success",
        "step": "arena_cars",
        "message": message,
        "buttons": buttons,
        "hasButtons": True,
        "dealership": {},
        "whatsapp_friendly": True
    }


def format_nexa_cars_message(nexa_cars: list, dealership: Dict[str, Any] = None) -> Dict[str, Any]:
    """Format Nexa cars listing with WhatsApp-friendly format"""
    message = f"✨ *NEXA SHOWROOM*\n"
    message += f"━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n"
    message += f"✨ *PREMIUM MODELS* ({len(nexa_cars)} cars)\n"
    message += f"─────────────────────────────\n"

    buttons = []
    for i, car in enumerate(nexa_cars, 1):
        message += f"{i}. *{car['name']}* - {car.get('category', 'Premium')}\n"

    message += f"\n👆 *Select a premium car to view details:*"


    return {
        "status": "success",
        "step": "nexa_cars",
        "message": message,
        "buttons": buttons,
        "hasButtons": True,
        "dealership": {},
        "whatsapp_friendly": True
    }


def format_search_results_message(search_result: Dict[str, Any]) -> Dict[str, Any]:
    """Format search results with WhatsApp-friendly format"""
    cars_text = f"🔍 *SEARCH RESULTS*\n"
    cars_text += "━━━━━━━━━━━━━━━━━━━━━\n"
    cars_text += f"Found {search_result['total_found']} matching car(s):\n\n"

    buttons = []
    for i, car in enumerate(search_result["cars"][:20], 1):  # Limit to 20 results
        dealership_type = car.get('dealership_type', 'Unknown')
        # emoji = "🏟️" if dealership_type == "Arena" else "⭐"
        cars_text += f"{i}. *{car['name']}* ({dealership_type})\n"


    cars_text += "\n👆 *Select a car for details:*"

    return {
        "status": "success",
        "message": cars_text,
        "buttons": buttons,
        "hasButtons": True,
        "whatsapp_friendly": True
    }

def format_car_details_from_data(car_data, arena_dealership_info=None, nexa_dealership_info=None):
    """
    Format car data into WhatsApp-friendly message
    """
    car_name = car_data.get('name', 'Unknown Car')
    category = car_data.get('category', 'Car')
    dealership_type = car_data.get('dealership_type', 'Arena')

    # Build detailed message using car data
    message = f"🚗 *{car_name.upper()}*\n"
    message += f"━━━━━━━━━━━━━━━━━━━━\n\n"
    message += f"🏷️ *Type:* {category}\n"
    message += f"🏪 *Showroom:* {dealership_type}\n\n"

    # Fuel Types
    fuel_types = car_data.get('fuel_types', [])
    if fuel_types:
        message += f"⛽ *Available Fuel Types:* {', '.join(fuel_types)}\n\n"

    # Engine Details
    engine_info = car_data.get('engine', {})
    if engine_info:
        message += "🔧 *ENGINE SPECIFICATIONS*\n"
        message += "─────────────────────────\n"
        for fuel_type, engine_data in engine_info.items():
            fuel_display = fuel_type.replace('_', ' ').title()
            message += f"• *{fuel_display}:* {engine_data.get('displacement', 'N/A')}\n"
            message += f"  Power: {engine_data.get('max_power', 'N/A')}\n"
            message += f"  Torque: {engine_data.get('max_torque', 'N/A')}\n"
        message += "\n"

    # Mileage
    mileage_info = car_data.get('mileage', {})
    if mileage_info:
        message += "⛽ *FUEL EFFICIENCY*\n"
        message += "──────────────────\n"
        for fuel_variant, mileage in mileage_info.items():
            fuel_display = fuel_variant.replace('_', ' ').title()
            message += f"• {fuel_display}: {mileage}\n"
        message += "\n"

    # Variants and Pricing
    variants = car_data.get('variants', [])
    if variants:
        message += "💰 *VARIANTS & PRICING*\n"
        message += "─────────────────────\n"
        for variant in variants[:4]:  # Show first 4 variants
            message += f"• *{variant.get('name', 'N/A')}* ({variant.get('fuel', 'Petrol')})\n"
            message += f"  {variant.get('transmission', 'Manual')} - {variant.get('price_range', 'Price on request')}\n"
        if len(variants) > 4:
            message += f"  ... and {len(variants) - 4} more variants\n"
        message += "\n"

    # Key Features
    features = car_data.get('features', [])
    if features:
        message += "✨ *KEY FEATURES*\n"
        message += "───────────────\n"
        for i, feature in enumerate(features[:6], 1):  # Show first 6 features
            message += f"{i}. {feature}\n"
        if len(features) > 6:
            message += f"   ... and {len(features) - 6} more features\n"
        message += "\n"

    # Dealership information is handled by LLM using knowledge_base.txt

    # Add cache info
    cache_metadata = car_data.get('cache_metadata', {})
    if cache_metadata:
        message += f"ℹ️ *Data cached on:* {cache_metadata.get('cached_at', 'N/A')[:20]}\n"

    response = {
        "status": "success",
        "step": "car_details",
        "message": message,
        "hasButtons": True,
        "carData": car_data,
        "dealership": {},
        "data_source": "specifications"
    }

    return response

def format_all_cars_message(all_cars_result: Dict[str, Any]) -> Dict[str, Any]:
    """Format all cars listing with WhatsApp-friendly format - Grouped by Dealership"""
    
    # Header section
    cars_text = "🚗 *ALL AVAILABLE CARS*\n"
    cars_text += "━━━━━━━━━━━━━━━━━━━━━━\n"
    cars_text += f"📊 Total: *{all_cars_result['total_found']}* cars available\n\n"

    buttons = []
    
    # Group cars by dealership type for better organization
    dealership_groups = {}
    for car in all_cars_result["cars"]:
        dealership_type = car.get('dealership_type', 'Unknown')
        if dealership_type not in dealership_groups:
            dealership_groups[dealership_type] = []
        dealership_groups[dealership_type].append(car)
    
    counter = 1
    
    # Sort dealership types (Arena first, then others alphabetically)
    sorted_dealerships = sorted(dealership_groups.keys(), 
                              key=lambda x: (x != 'Arena', x))
    
    for dealership_type in sorted_dealerships:
        cars = dealership_groups[dealership_type]
        
        # Dealership header with emoji
        emoji = "🏟️" if dealership_type == "Arena" else "⭐"
        cars_text += f"{emoji} *{dealership_type.upper()} DEALERSHIP*\n\n"
        # cars_text += "─────────────────────────\n"
        
        # List cars under this dealership
        for car in cars:
            car_name = car.get('name', 'Unknown Car')
            cars_text += f"  {counter:2d}. *{car_name}*\n"
            
            counter += 1
        
        cars_text += "\n"  # Space between dealership sections
    
    # Footer section
    # cars_text += "━━━━━━━━━━━━━━━━━━━━━━━━━━\n"
    cars_text += "👆 *Select any car for detailed information*"

    return {
        "status": "success",
        "message": cars_text,
        "buttons": buttons,
        "hasButtons": True,
        "whatsapp_friendly": True
    }
def format_cars_message(cars: list, dealership: dict = None, dealership_type: str = "arena") -> dict:
    """
    Format car list for WhatsApp based on dealership type (arena/nexa).
    """
    is_nexa = dealership_type.lower() == "nexa"
    header_emoji = "✨" if is_nexa else "🏟️"
    header_title = "NEXA SHOWROOM" if is_nexa else "ARENA SHOWROOM"
    model_title = "PREMIUM MODELS" if is_nexa else "AVAILABLE MODELS"

    message = f"{header_emoji} *{header_title}*\n"
    message += f"━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n"
    message += f"🚘 *{model_title}* ({len(cars)} cars)\n"
    message += f"─────────────────────────────\n"

    buttons = []
    for i, car in enumerate(cars, 1):
        # emoji = "⭐" if is_nexa else "🚙" if "SUV" in car.get("category", "") else "🚗"
        car_name = car.get("name", "Unknown Car")
        message += f"{i}. *{car_name}* - {car.get('category', 'Unknown')}\n"

    message += "\n👆 *Select a car to view details:*"

    return {
        "status": "success",
        "step": f"{dealership_type.lower()}_cars",
        "message": message,
        "buttons": buttons,
        "hasButtons": True,
        "dealership": {},
        "whatsapp_friendly": True
    }
